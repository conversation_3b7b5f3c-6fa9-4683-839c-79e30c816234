"""MCP server implementation for SwarmX agents."""

import json
import logging
from typing import Any, Dict, List, TypedDict

from mcp.server import FastMC<PERSON>
from mcp.types import CallToolR<PERSON>ult, TextContent
from openai.types.chat import ChatCompletionMessageParam
from pydantic import BaseModel

from .agent import Agent

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class AgentRequest(BaseModel):
    """Request model for agent execution."""
    
    messages: List[ChatCompletionMessageParam]
    context: Dict[str, Any] | None = None


class AgentResponse(BaseModel):
    """Response model for agent execution."""

    messages: List[ChatCompletionMessageParam]


class MCPToolOutput(TypedDict):
    """TypedDict for MCP tool structured output."""

    messages: List[ChatCompletionMessageParam]
    context: Dict[str, Any] | None


def create_mcp_server(agent: Agent) -> FastMCP:
    """Create an MCP server that exposes a SwarmX agent as a tool.
    
    Args:
        agent: The SwarmX agent to expose as an MCP tool
        
    Returns:
        FastMCP server instance
    """
    # Create the MCP server
    mcp = FastMCP(f"swarmx-{agent.name}")
    
    @mcp.tool()
    async def run_agent(
        messages: List[Dict[str, Any]],
        context: Dict[str, Any] | None = None
    ) -> CallToolResult:
        """Run the SwarmX agent with the provided messages.

        Args:
            messages: List of chat completion messages to send to the agent
            context: Optional context variables to pass to the agent

        Returns:
            CallToolResult with structured output containing messages and context
        """
        try:
            # Convert dict messages to proper ChatCompletionMessageParam format
            formatted_messages: List[ChatCompletionMessageParam] = []
            for msg in messages:
                formatted_messages.append(msg)  # type: ignore

            # Run the agent (non-streaming for MCP compatibility)
            result_messages = await agent.run(
                messages=formatted_messages,
                stream=False,
                context=context
            )

            # Create structured output for MCP tool hooks
            structured_output: MCPToolOutput = {
                "messages": result_messages,
                "context": context
            }

            # Return CallToolResult with structured output
            return CallToolResult(
                content=[TextContent(
                    type="text",
                    text=f"Agent '{agent.name}' processed {len(messages)} input messages and generated {len(result_messages)} response messages."
                )],
                structuredOutput=structured_output
            )

        except Exception as e:
            logger.error(f"Error running agent: {e}")
            return CallToolResult(
                content=[TextContent(
                    type="text",
                    text=f"Error running agent: {str(e)}"
                )],
                isError=True
            )
    
    return mcp


def create_multi_agent_mcp_server(agents: Dict[str, Agent]) -> FastMCP:
    """Create an MCP server that exposes multiple SwarmX agents as tools.
    
    Args:
        agents: Dictionary mapping agent names to Agent instances
        
    Returns:
        FastMCP server instance
    """
    # Create the MCP server
    mcp = FastMCP("swarmx-multi-agent")
    
    # Create a tool for each agent
    for agent_name, agent in agents.items():
        
        # Create a closure to capture the agent
        def create_agent_tool(agent_instance: Agent, name: str):
            @mcp.tool(name=f"run_{name}")
            async def run_specific_agent(
                messages: List[Dict[str, Any]],
                context: Dict[str, Any] | None = None
            ) -> CallToolResult:
                f"""Run the {name} agent with the provided messages.

                Args:
                    messages: List of chat completion messages to send to the agent
                    context: Optional context variables to pass to the agent

                Returns:
                    CallToolResult with structured output containing messages and context
                """
                try:
                    # Convert dict messages to proper ChatCompletionMessageParam format
                    formatted_messages: List[ChatCompletionMessageParam] = []
                    for msg in messages:
                        formatted_messages.append(msg)  # type: ignore

                    # Run the agent (non-streaming for MCP compatibility)
                    result_messages = await agent_instance.run(
                        messages=formatted_messages,
                        stream=False,
                        context=context
                    )

                    # Create structured output for MCP tool hooks
                    structured_output: MCPToolOutput = {
                        "messages": result_messages,
                        "context": context
                    }

                    # Return CallToolResult with structured output
                    return CallToolResult(
                        content=[TextContent(
                            type="text",
                            text=f"Agent '{name}' processed {len(messages)} input messages and generated {len(result_messages)} response messages."
                        )],
                        structuredOutput=structured_output
                    )

                except Exception as e:
                    logger.error(f"Error running agent {name}: {e}")
                    return CallToolResult(
                        content=[TextContent(
                            type="text",
                            text=f"Error running agent {name}: {str(e)}"
                        )],
                        isError=True
                    )

            return run_specific_agent

        # Create tool for each agent
        create_agent_tool(agent, agent_name)
    
    return mcp
