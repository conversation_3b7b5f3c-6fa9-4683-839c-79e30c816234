#!/usr/bin/env python3
"""Example of using SwarmX as an MCP server."""

import asyncio
import json
from pathlib import Path

from swarmx import Agent, create_mcp_server, create_multi_agent_mcp_server


async def example_single_agent_mcp_server():
    """Example: Create and run a single agent MCP server."""
    print("Example: Single Agent MCP Server")
    print("-" * 40)
    
    # Create an agent
    agent = Agent(
        name="HelperAgent",
        instructions="You are a helpful assistant that provides clear and concise answers.",
        model="gpt-3.5-turbo"
    )
    
    # Create MCP server
    mcp_server = create_mcp_server(agent)
    
    print(f"Created MCP server: {mcp_server.name}")
    print("To use this server with an MCP client, run:")
    print("  python -m swarmx mcp-server --file agent_config.json")
    print()
    
    return mcp_server


async def example_multi_agent_mcp_server():
    """Example: Create and run a multi-agent MCP server."""
    print("Example: Multi-Agent MCP Server")
    print("-" * 40)
    
    # Create multiple agents
    agents = {
        "assistant": Agent(
            name="Assistant",
            instructions="You are a general-purpose helpful assistant.",
            model="gpt-3.5-turbo"
        ),
        "coder": Agent(
            name="Coder",
            instructions="You are a coding assistant that helps with programming tasks.",
            model="gpt-4"
        ),
        "translator": Agent(
            name="Translator",
            instructions="You are a language translator that can translate between different languages.",
            model="gpt-3.5-turbo"
        )
    }
    
    # Create multi-agent MCP server
    mcp_server = create_multi_agent_mcp_server(agents)
    
    print(f"Created multi-agent MCP server: {mcp_server.name}")
    print("This server exposes the following tools:")
    for agent_name in agents:
        print(f"  - run_{agent_name}: Run the {agent_name} agent")
    print()
    
    return mcp_server


def create_example_agent_configs():
    """Create example agent configuration files."""
    print("Creating example agent configuration files...")
    print("-" * 40)
    
    # Single agent config
    single_agent_config = {
        "name": "ExampleAgent",
        "instructions": "You are a helpful assistant that provides clear and informative responses.",
        "model": "gpt-3.5-turbo"
    }
    
    with open("example_agent.json", "w") as f:
        json.dump(single_agent_config, f, indent=2)
    
    print("Created: example_agent.json")
    
    # Multi-agent config (for future use)
    multi_agent_config = {
        "name": "MainAgent",
        "instructions": "You are a coordinator agent that can delegate tasks to specialized agents.",
        "model": "gpt-4",
        "nodes": [
            {
                "name": "SpecialistAgent",
                "instructions": "You are a specialist agent that handles specific domain tasks.",
                "model": "gpt-3.5-turbo"
            }
        ]
    }
    
    with open("example_multi_agent.json", "w") as f:
        json.dump(multi_agent_config, f, indent=2)
    
    print("Created: example_multi_agent.json")
    print()


def show_usage_examples():
    """Show usage examples for the MCP server."""
    print("Usage Examples")
    print("=" * 40)
    print()
    
    print("1. Start MCP server with a single agent:")
    print("   python -m swarmx mcp-server --file example_agent.json")
    print()
    
    print("2. Start MCP server with custom transport:")
    print("   python -m swarmx mcp-server --file example_agent.json --transport stdio")
    print()
    
    print("3. Use in Claude Desktop (add to claude_desktop_config.json):")
    print('   {')
    print('     "mcpServers": {')
    print('       "swarmx": {')
    print('         "command": "python",')
    print('         "args": ["-m", "swarmx", "mcp-server", "--file", "/path/to/example_agent.json"]')
    print('       }')
    print('     }')
    print('   }')
    print()
    
    print("4. Programmatic usage:")
    print("   from swarmx import Agent, create_mcp_server")
    print("   agent = Agent(name='MyAgent', instructions='...')")
    print("   server = create_mcp_server(agent)")
    print("   server.run(transport='stdio')")
    print()


async def main():
    """Run all examples."""
    print("SwarmX MCP Server Examples")
    print("=" * 50)
    print()
    
    # Create example configurations
    create_example_agent_configs()
    
    # Show single agent example
    await example_single_agent_mcp_server()
    
    # Show multi-agent example
    await example_multi_agent_mcp_server()
    
    # Show usage examples
    show_usage_examples()
    
    print("Examples completed! Check the created JSON files and try running the MCP server.")


if __name__ == "__main__":
    asyncio.run(main())
